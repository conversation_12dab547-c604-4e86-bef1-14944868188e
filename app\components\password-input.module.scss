.password-input-container {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
  margin-bottom: 15px;

  .password-input {
    width: 100%;
    border: var(--border-in-light);
    border-radius: 10px;
    padding: 10px;
    padding-right: 40px; /* 为眼睛图标留出空间 */
    font-family: inherit;
    background-color: var(--white);
    color: var(--black);
    resize: none;
    min-width: 50px;
    max-width: 300px;
    height: 40px;
    box-sizing: border-box;
  }

  .password-eye {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
    background: none;
    border: none;
    padding: 0;
    margin: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--black);
    opacity: 0.6;
    width: 20px;
    height: 20px;

    svg {
      width: 16px;
      height: 16px;
    }

    &:hover {
      opacity: 1;
    }
  }
}

.input-row {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  gap: 10px;
  width: 100%;

  .input-label {
    min-width: 80px;
    font-size: 14px;
    text-align: right;
  }

  .input-field {
    flex: 1;
    width: 100%;
  }
}

:global(html.dark) {
  .password-input-container {
    .password-input {
      background-color: var(--dark-50);
      color: var(--white);
      border-color: var(--dark-border);
    }

    .password-eye {
      color: var(--white);
    }
  }
}
