// 测试跨提供商摘要功能的修复
// 模拟当聊天模型是Gemini，摘要模型是SiliconFlow时的情况

// 模拟 ServiceProvider 枚举
const ServiceProvider = {
  OpenAI: "OpenAI",
  Google: "Google", 
  SiliconFlow: "SiliconFlow",
  Anthropic: "Anthropic",
  DeepSeek: "DeepSeek"
};

// 模拟 getClientApi 函数
function getClientApi(provider) {
  console.log(`[getClientApi] Creating API client for provider: ${provider}`);
  return {
    llm: {
      chat: (options) => {
        console.log(`[${provider} API] Chat called with:`, {
          model: options.config.model,
          providerName: options.config.providerName,
          messageCount: options.messages.length
        });
        
        // 模拟成功响应
        setTimeout(() => {
          if (options.onFinish) {
            options.onFinish("Generated title/summary", { status: 200 });
          }
        }, 100);
      }
    }
  };
}

// 测试场景1：聊天模型和摘要模型使用相同提供商
function testSameProvider() {
  console.log("\n=== 测试场景1：相同提供商 ===");
  
  const session = {
    mask: {
      modelConfig: {
        model: "gemini-2.5-pro-preview-05-06",
        providerName: "Google"
      }
    }
  };
  
  const model = "gemini-pro";
  const providerName = "Google";
  
  // 原始API客户端（基于聊天模型）
  const api = getClientApi(session.mask.modelConfig.providerName);
  
  // 修复后的逻辑：检查是否需要不同的API客户端
  const titleApi = providerName !== session.mask.modelConfig.providerName 
    ? getClientApi(providerName)
    : api;
  
  console.log(`聊天模型: ${session.mask.modelConfig.model} (${session.mask.modelConfig.providerName})`);
  console.log(`摘要模型: ${model} (${providerName})`);
  console.log(`使用不同API客户端: ${providerName !== session.mask.modelConfig.providerName}`);
  
  titleApi.llm.chat({
    messages: [],
    config: {
      model,
      stream: false,
      providerName,
    },
    onFinish(message, responseRes) {
      console.log(`✅ 标题生成成功: ${message}`);
    }
  });
}

// 测试场景2：聊天模型和摘要模型使用不同提供商
function testDifferentProvider() {
  console.log("\n=== 测试场景2：不同提供商 ===");
  
  const session = {
    mask: {
      modelConfig: {
        model: "gemini-2.5-pro-preview-05-06",
        providerName: "Google"
      }
    }
  };
  
  const model = "deepseek-ai/DeepSeek-V3";
  const providerName = "SiliconFlow";
  
  // 原始API客户端（基于聊天模型）
  const api = getClientApi(session.mask.modelConfig.providerName);
  
  // 修复后的逻辑：检查是否需要不同的API客户端
  const titleApi = providerName !== session.mask.modelConfig.providerName 
    ? getClientApi(providerName)
    : api;
  
  console.log(`聊天模型: ${session.mask.modelConfig.model} (${session.mask.modelConfig.providerName})`);
  console.log(`摘要模型: ${model} (${providerName})`);
  console.log(`使用不同API客户端: ${providerName !== session.mask.modelConfig.providerName}`);
  
  titleApi.llm.chat({
    messages: [],
    config: {
      model,
      stream: false,
      providerName,
    },
    onFinish(message, responseRes) {
      console.log(`✅ 标题生成成功: ${message}`);
    }
  });
}

// 测试场景3：摘要功能
function testSummarizeFunction() {
  console.log("\n=== 测试场景3：摘要功能 ===");
  
  const session = {
    mask: {
      modelConfig: {
        model: "gemini-2.5-pro-preview-05-06",
        providerName: "Google"
      }
    }
  };
  
  const model = "deepseek-ai/DeepSeek-V3";
  const providerName = "SiliconFlow";
  
  // 原始API客户端（基于聊天模型）
  const api = getClientApi(session.mask.modelConfig.providerName);
  
  // 修复后的逻辑：检查是否需要不同的API客户端
  const summarizeApi = providerName !== session.mask.modelConfig.providerName 
    ? getClientApi(providerName)
    : api;
  
  console.log(`聊天模型: ${session.mask.modelConfig.model} (${session.mask.modelConfig.providerName})`);
  console.log(`摘要模型: ${model} (${providerName})`);
  console.log(`使用不同API客户端: ${providerName !== session.mask.modelConfig.providerName}`);
  
  summarizeApi.llm.chat({
    messages: [],
    config: {
      model,
      stream: true,
      providerName,
    },
    onUpdate(message) {
      console.log(`📝 摘要更新: ${message}`);
    },
    onFinish(message, responseRes) {
      console.log(`✅ 摘要生成成功: ${message}`);
    }
  });
}

// 运行测试
console.log("=== 测试跨提供商摘要功能修复 ===");

testSameProvider();
testDifferentProvider();
testSummarizeFunction();

console.log("\n=== 修复说明 ===");
console.log("1. 当摘要模型和聊天模型使用相同提供商时，复用现有API客户端");
console.log("2. 当摘要模型和聊天模型使用不同提供商时，创建新的API客户端");
console.log("3. 这确保了正确的API端点和认证信息被使用");
console.log("4. 解决了401未授权错误的根本原因");
