# 자주 묻는 질문

## 어떻게 빠르게 도움을 받을 수 있나요?

1. ChatGPT / Bing / Baidu / Google 등에 질문합니다.
2. 인터넷 사용자에게 질문합니다. 문제의 배경 정보와 자세한 문제 설명을 제공하세요. 질 좋은 질문은 유용한 답변을 쉽게 받을 수 있습니다.

# 배포 관련 질문

각종 배포 방법에 대한 자세한 튜토리얼 참조: [링크](https://rptzik3toh.feishu.cn/docx/XtrdduHwXoSCGIxeFLlcEPsdn8b)

## 왜 Docker 배포 버전이 계속 업데이트 알림을 주나요?

Docker 버전은 사실상 안정된 버전과 같습니다. latest Docker는 항상 latest release version과 일치합니다. 현재 우리의 발행 빈도는 하루 또는 이틀에 한 번이므로 Docker 버전은 항상 최신 커밋보다 하루나 이틀 뒤처집니다. 이것은 예상된 것입니다.

## Vercel에서 어떻게 배포하나요?

1. Github 계정을 등록하고, 이 프로젝트를 포크합니다.
2. Vercel을 등록합니다(휴대폰 인증 필요, 중국 번호 사용 가능), Github 계정을 연결합니다.
3. Vercel에서 새 프로젝트를 생성하고, Github에서 포크한 프로젝트를 선택합니다. 환경 변수를 필요에 따라 입력한 후 배포를 시작합니다. 배포 후에는 VPN이 있는 환경에서 Vercel이 제공하는 도메인으로 프로젝트에 접근할 수 있습니다.
4. 중국에서 방화벽 없이 접근하려면: 도메인 관리 사이트에서 도메인의 CNAME 레코드를 추가하고, cname.vercel-dns.com을 가리키게 합니다. 그런 다음 Vercel에서 도메인 접근을 설정합니다.

## Vercel 환경 변수를 어떻게 수정하나요?

- Vercel의 제어판 페이지로 이동합니다.
- NextChat 프로젝트를 선택합니다.
- 페이지 상단의 Settings 옵션을 클릭합니다.
- 사이드바의 Environment Variables 옵션을 찾습니다.
- 해당 값을 수정합니다.

## 환경 변수 CODE는 무엇이며, 반드시 설정해야 하나요?

이것은 당신이 사용자 정의한 접근 비밀번호입니다. 다음 중 하나를 선택할 수 있습니다:

1. 설정하지 않습니다. 해당 환경 변수를 삭제합니다. 주의: 이 경우 누구나 프로젝트에 접근할 수 있습니다.
2. 프로젝트를 배포할 때 환경 변수 CODE를 설정합니다(여러 비밀번호는 쉼표로 구분). 접근 비밀번호를 설정하면 사용자는 설정 페이지에서 접근 비밀번호를 입력해야만 사용할 수 있습니다. [관련 설명 참조](https://github.com/Yidadaa/ChatGPT-Next-Web/blob/main/README_CN.md#%E9%85%8D%E7%BD%AE%E9%A1%B5%E9%9D%A2%E8%AE%BF%E9%97%AE%E5%AF%86%E7%A0%81)

## 왜 내 배포 버전에 스트리밍 응답이 없나요?

> 관련 토론: [#386](https://github.com/Yidadaa/ChatGPT-Next-Web/issues/386)

nginx 리버스 프록시를 사용하는 경우, 설정 파일에 다음 코드를 추가해야 합니다:

```nginx
# 캐시하지 않고, 스트리밍 출력 지원
proxy_cache off;  # 캐시 비활성화
proxy_buffering off;  # 프록시 버퍼링 비활성화
chunked_transfer_encoding on;  # 청크 전송 인코딩 활성화
tcp_nopush on;  # TCP NOPUSH 옵션 활성화, Nagle 알고리즘 금지
tcp_nodelay on;  # TCP NODELAY 옵션 활성화, 지연 ACK 알고리즘 금지
keepalive_timeout 300;  # keep-alive 타임아웃을 65초로 설정
```

netlify에서 배포하는 경우, 이 문제는 아직 해결되지 않았습니다. 기다려 주십시오.

## 배포했지만 액세스할 수 없는 경우.

다음의 사항들을 확인해보세요:

- 서비스가 배포 중인가요?
- 포트가 올바르게 매핑되었나요?
- 방화벽에서 포트가 열렸나요?
- 서버 경로가 유효한가요?
- 도메인 이름이 올바른가요?

## 프록시란 무엇이며 어떻게 사용하나요?

중국 및 일부 국가에서는 OpenAI의 IP 제한으로 인해 OpenAI API에 직접 연결할 수 없으며 프록시를 거쳐야 합니다. 프록시 서버(정방향 프록시)를 사용하거나 OpenAI API에 대해 설정된 역방향 프록시를 사용할 수 있습니다.

- 정방향 프록시 예: 사이언티픽 인터넷 래더. 도커 배포의 경우 환경 변수 HTTP_PROXY를 프록시 주소(예: ***********:8002)로 설정합니다.
- 역방향 프록시 예: 다른 사람이 구축한 프록시 주소를 사용하거나 Cloudflare를 통해 무료로 설정할 수 있습니다. 프로젝트 환경 변수 BASE_URL을 프록시 주소로 설정합니다.

## 국내 서버를 배포할 수 있나요?

예. 하지만 해결해야 할 문제가 있습니다:

- github 및 openAI와 같은 사이트에 연결하려면 프록시가 필요합니다;
- 도메인 이름 확인을 설정하려면 국내 서버를 신청해야 합니다;
- 국내 정책에 따라 프록시가 엑스트라넷/ChatGPT 관련 애플리케이션에 액세스하지 못하도록 제한되어 차단될 수 있습니다.

## 도커 배포 후 네트워크 오류가 발생하는 이유는 무엇인가요?

https://github.com/Yidadaa/ChatGPT-Next-Web/issues/1569 에서 토론을 참조하세요.

## 사용 관련 문제

## "문제가 발생했습니다, 나중에 다시 시도하세요"라는 메시지가 계속 뜨는 이유는 무엇인가요?

여러 가지 이유가 있을 수 있으니 순서대로 확인해 주세요:

- 코드 버전이 최신 버전인지 확인하고, 최신 버전으로 업데이트한 후 다시 시도해 주세요;
- API 키가 올바르게 설정되었는지 확인해주세요. 환경 변수 이름은 모두 대문자이며 밑줄이 있어야 합니다;
- API 키가 사용 가능한지 확인해 주세요;
- 위 단계를 수행한 후에도 문제를 확인할 수 없는 경우, 이슈 영역에 신규 이슈를 제출하고 버셀의 런타임 로그 또는 도커 런타임 로그를 첨부해 주시기 바랍니다.

## ChatGPT 응답이 왜곡되는 이유는 무엇인가요?

설정 - 모델 설정 섹션에 '온도'에 대한 값이 있는데, 이 값이 1보다 크면 응답이 왜곡될 수 있으니 1 이내로 다시 설정해 주세요.

## "권한이 없는 상태입니다, 설정 페이지에서 액세스 비밀번호를 입력하세요"?

프로젝트에서 환경 변수 CODE에 접근 비밀번호를 설정했습니다. 처음 사용할 때는 설정 페이지에서 액세스 코드를 입력해야 합니다.

## 사용 시 "현재 할당량을 초과했습니다, ..."라는 메시지가 표시됩니다.

API 키에 문제가 있습니다. 잔액이 부족합니다.

## "오류: CSS 청크 xxx를 로드하지 못했습니다..."와 함께 사용.

첫 번째 화이트 스크린 시간을 줄이기 위해 청크 컴파일이 기본적으로 활성화되어 있으며, 기술 원칙은 아래를 참조하세요:

- https://nextjs.org/docs/app/building-your-application/optimizing/lazy-loading
- https://stackoverflow.com/questions/55993890/how-can-i-disable-chunkcode-splitting-with-webpack4
- https://github.com/vercel/next.js/issues/38507
- https://stackoverflow.com/questions/55993890/how-can-i-disable-chunkcode-splitting-with-webpack4

그러나 NextJS는 호환성이 좋지 않아 구형 브라우저에서 이 오류가 발생할 수 있으므로 컴파일 시 청크 컴파일을 비활성화할 수 있습니다.

버셀 플랫폼의 경우 환경 변수에 `DISABLE_CHUNK=1`을 추가하고 다시 배포합니다;
자체 컴파일 및 배포한 프로젝트의 경우, 빌드 시 `DISABLE_CHUNK=1 yarn build`를 사용하여 빌드합니다;
Docker 사용자의 경우, Docker가 프로젝트를 패키징할 때 이미 빌드하기 때문에 이 기능을 해제하는 것은 지원되지 않습니다.

이 기능을 끄면 사용자가 웹사이트를 처음 방문할 때 모든 리소스를 로드하므로 인터넷 연결 상태가 좋지 않은 경우 흰색 화면이 길게 표시되어 사용자 경험에 영향을 줄 수 있으므로 사용자가 직접 고려하시기 바랍니다.

"## NotFoundError: '노드': 노드....에서 'removeChild'를 실행하지 못했습니다." 오류가 발생했습니다.
브라우저의 자체 자동 번역 기능을 비활성화하고 모든 자동 번역 플러그인을 닫아주세요.

## 웹 서비스 관련 문제

## 클라우드플레어란 무엇인가요?

Cloudflare(CF)는 CDN, 도메인 관리, 정적 페이지 호스팅, 엣지 컴퓨팅 기능 배포 등을 제공하는 웹 서비스 제공업체입니다. 일반적인 용도: 도메인 구매 및/또는 호스팅(리졸브, 동적 도메인 등), 서버에 CDN 설치(벽에서 IP를 숨기는 기능), 웹사이트 배포(CF 페이지). CF는 이러한 서비스 대부분을 무료로 제공합니다.

## Vercel이란 무엇인가요?

Vercel은 개발자가 최신 웹 애플리케이션을 더 빠르게 빌드하고 배포할 수 있도록 설계된 글로벌 클라우드 플랫폼입니다. 이 프로젝트와 많은 웹 애플리케이션을 클릭 한 번으로 Vercel에 무료로 배포할 수 있습니다. 코드, 리눅스, 서버, 수수료가 필요 없고 OpenAI API 프록시를 설정할 필요도 없습니다. 단점은 중국에서 장벽 없이 액세스하려면 도메인 이름을 바인딩해야 한다는 것입니다.

## 도메인 네임은 어떻게 얻나요?

1) 도메인 네임 공급업체로 이동하여 해외에서는 Namesilo(알리페이 지원), 클라우드플레어 등, 중국에서는 월드와이드웹과 같은 도메인 네임을 등록합니다. 2) 무료 도메인 네임 공급업체: 예: eBay;
2. 무료 도메인 네임 제공업체: eu.org(두 번째 레벨 도메인 네임) 등..;
3. 친구에게 무료 2단계 도메인 네임을 요청합니다.

## 서버를 얻는 방법

- 외국 서버 제공업체의 예: 아마존 클라우드, 구글 클라우드, 벌터, 밴드왜건, 호스트데어 등;
  해외 서버 문제: 서버 라인은 해당 국가의 액세스 속도에 영향을 미치므로 CN2 GIA 및 CN2 라인 서버를 권장합니다. 국내 서버의 접속에 문제가 있는 경우(심각한 패킷 손실 등) CDN(Cloudflare 및 기타 제공 업체)을 설정해 볼 수 있습니다.
- 국내 서버 제공업체: 알리윈, 텐센트 등;
  국내 서버 문제: 도메인 이름 확인을 신청해야 하며, 국내 서버 대역폭이 더 비싸고, 해외 사이트(Github, openAI 등)에 액세스하려면 프록시가 필요합니다.

## 서버는 언제 신청해야 하나요?

중국 본토에서 운영되는 웹사이트는 규제 요건에 따라 신고해야 합니다. 실제로 서버가 중국에 있고 도메인 네임 레졸루션이 있는 경우 서버 제공업체가 규제 신고 요건을 시행하며, 그렇지 않으면 서비스가 종료됩니다. 일반적인 규칙은 다음과 같습니다:
|서버 위치|도메인 네임 공급자|파일링 필요 여부|
|---|---|---|
|국내|국내|예
|국내|외국|예
|외국|외국인|아니요
|외국|국내|일반적으로 아니요|

서버 공급자를 전환한 후 파일링을 전환해야 합니다.

## OpenAI 관련 질문

## OpenAI 계정은 어떻게 가입하나요?

chat.openai.com으로 이동하여 등록하세요. 다음이 필요합니다:

- 유효한 래더(OpenAI는 지역별 기본 IP 주소를 지원합니다)
- 지원되는 이메일 주소(예: Outlook이나 qq가 아닌 Gmail 또는 회사/학교 이메일)
- SMS 인증을 받을 수 있는 방법(예: SMS 활성화 웹사이트)

## OpenAI API는 어떻게 열 수 있나요? API 잔액은 어떻게 확인하나요?

공식 웹사이트 주소(래더 필요): https://platform.openai.com/account/usage
일부 사용자는 래더 없이 잔액 조회 에이전트를 구축한 경우가 있으니, 해당 사용자에게 요청해 주시기 바랍니다. API 키 유출을 방지하기 위해 신뢰할 수 있는 소스인지 확인하시기 바랍니다.

## 새로 등록한 OpenAI 계정에 API 잔액이 없는 이유는 무엇인가요?

(4월 6일 업데이트) 새로 등록된 계정은 일반적으로 24시간 후에 API 잔액이 표시됩니다. 현재 새로 등록된 계정에는 $5의 잔액이 표시됩니다.

## OpenAI API를 충전하려면 어떻게 해야 하나요?

OpenAI는 특정 지역의 신용카드만 사용할 수 있습니다(중국 신용카드는 사용할 수 없음). 충전 방법의 몇 가지 예는 다음과 같습니다:

1. 가상 신용카드로 결제하기
2. 해외 신용카드 신청
3. 온라인에서 신용카드를 충전할 사람 찾기

## GPT-4 API 액세스는 어떻게 사용하나요?

- GPT-4 API 액세스는 별도의 신청이 필요합니다. 다음 주소로 이동하여 정보를 입력하여 신청 대기열 대기자 명단에 들어가세요(OpenAI 조직 ID를 준비하세요): https://openai.com/waitlist/gpt-4-api.
  그런 다음 이메일 메시지를 기다립니다.
- ChatGPT Plus를 사용하도록 설정했다고 해서 GPT-4 권한이 있는 것은 아니며, 서로 관련이 없습니다.

## Azure OpenAI 인터페이스 사용 방법

참조: [#371](https://github.com/Yidadaa/ChatGPT-Next-Web/issues/371)

## 내 토큰이 왜 이렇게 빨리 소모되나요?

> 관련 토론: [#518](https://github.com/Yidadaa/ChatGPT-Next-Web/issues/518)

- GPT 4에 액세스 권한이 있고 매일 GPT 4 API를 사용하는 경우, GPT 4 가격이 GPT 3.5의 약 15배이기 때문에 청구 금액이 급격히 증가합니다;
- GPT 3.5를 자주 사용하지 않는데도 요금이 급격하게 증가하는 경우 아래 단계를 따라 확인하시기 바랍니다:
  - 오픈아이 공식 웹사이트로 이동하여 API 키 소비 기록을 확인하고, 매 시간마다 토큰이 소비되고 매번 수만 개의 토큰이 소비된다면 키가 유출된 것이므로 즉시 삭제하고 재생성하시기 바랍니다. 즉시 키를 삭제하고 다시 생성하시기 바랍니다. 지저분한 웹사이트에서 잔액을 확인하지 마세요. **
  - 비밀번호 설정이 5자리 이내의 문자와 같이 매우 짧으면 블라스팅 비용이 매우 낮습니다. 도커의 로그 기록을 검색하여 누군가 많은 수의 비밀번호 조합을 시도했는지 확인하는 것이 좋습니다. 키워드: 액세스 코드를 얻었습니다.
- 이 두 가지 방법을 사용하면 토큰이 소비되는 이유를 빠르게 찾을 수 있습니다:
  - 오픈아이 소비 기록은 비정상적이지만 도커 로그는 정상이라면 API 키가 유출되고 있다는 뜻입니다;
  - 도커 로그에서 액세스 코드 버스트 레코드가 많이 발견되면 비밀번호가 버스트된 것입니다.


## API의 가격은 어떻게 청구되나요?

OpenAI의 청구 지침은 https://openai.com/pricing#language-models 에서 확인할 수 있습니다.  
OpenAI는 토큰 수에 따라 요금을 청구하며, 일반적으로 1000토큰은 영어 단어 750개 또는 중국어 문자 500개를 나타냅니다. 입력(프롬프트)과 출력(완료)은 별도로 청구됩니다.

|모델|사용자 입력(프롬프트) 청구 |모델 출력(완료) 청구 |인터랙션당 최대 토큰 수 |
|----|----|----|----|
|GPT-3.5-TURBO|$0.0015 / 1천 토큰|$0.002 / 1천 토큰|4096|
|GPT-3.5-TURBO-16K|$0.003 / 1천 토큰|$0.004 / 1천 토큰|16384| |GPT-4|$0.004 / 1천 토큰|16384
|GPT-3.5-TURBO-16K|$0.003 / 1천 토큰|$0.004 / 1천 토큰|16384| |GPT-4|$0.03 / 1천 토큰|$0.06 / 1천 토큰|8192
|GPT-4-32K|$0.06 / 1천 토큰|$0.12 / 1천 토큰|32768|

## gpt-3.5-터보와 gpt3.5-터보-0301(또는 gpt3.5-터보-mmdd) 모델의 차이점은 무엇인가요?

공식 문서 설명: https://platform.openai.com/docs/models/gpt-3-5

- GPT-3.5-TURBO는 최신 모델이며 지속적으로 업데이트될 예정입니다.
- gpt-3.5-turbo-0301은 3월 1일에 고정된 모델의 스냅샷으로, 변경되지 않으며 3개월 후에 새로운 스냅샷으로 대체될 예정입니다.