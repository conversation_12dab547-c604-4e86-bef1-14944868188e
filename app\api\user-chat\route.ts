import { NextRequest, NextResponse } from "next/server";
import fs from "fs/promises";
import path from "path";
import { auth } from "../auth";
import { ModelProvider } from "@/app/constant";

// 用户对话数据目录
const USER_CHAT_DIR = path.join(process.cwd(), "app/user-chats");

// 确保用户对话数据目录存在
async function ensureUserChatDir() {
  try {
    await fs.mkdir(USER_CHAT_DIR, { recursive: true });
  } catch (error) {
    console.error("Failed to create user chat directory:", error);
  }
}

// 获取用户对话数据文件路径
function getUserChatPath(username: string) {
  return path.join(USER_CHAT_DIR, `${username}.json`);
}

// 获取用户对话数据
async function getUserChat(username: string) {
  try {
    const chatPath = getUserChatPath(username);
    const chatData = await fs.readFile(chatPath, 'utf-8');
    return JSON.parse(chatData);
  } catch (error) {
    console.log(`No chat data found for user ${username}, returning empty data`);
    return { sessions: [], currentSessionIndex: 0 };
  }
}

// 保存用户对话数据
async function saveUserChat(username: string, chatData: any) {
  try {
    await ensureUserChatDir();
    const chatPath = getUserChatPath(username);
    await fs.writeFile(chatPath, JSON.stringify(chatData, null, 2), 'utf-8');
    return true;
  } catch (error) {
    console.error(`Failed to save chat data for user ${username}:`, error);
    return false;
  }
}

// 处理 GET 请求 - 获取用户对话数据
export async function GET(req: NextRequest) {
  // 验证用户身份
  const authResult = auth(req, ModelProvider.GPT);
  if (authResult.error) {
    return NextResponse.json(authResult, {
      status: 401,
    });
  }

  // 从URL参数中获取用户名
  const url = new URL(req.url);
  const username = url.searchParams.get("username");

  if (!username) {
    return NextResponse.json(
      { error: true, message: "Username is required" },
      { status: 400 }
    );
  }

  // 获取用户对话数据
  const chatData = await getUserChat(username);

  return NextResponse.json(chatData);
}

// 处理 POST 请求 - 保存用户对话数据
export async function POST(req: NextRequest) {
  // 验证用户身份
  const authResult = auth(req, ModelProvider.GPT);
  if (authResult.error) {
    return NextResponse.json(authResult, {
      status: 401,
    });
  }

  try {
    const body = await req.json();
    const { username, chatData } = body;

    if (!username) {
      return NextResponse.json(
        { error: true, message: "Username is required" },
        { status: 400 }
      );
    }

    if (!chatData) {
      return NextResponse.json(
        { error: true, message: "Chat data is required" },
        { status: 400 }
      );
    }

    // 保存用户对话数据
    const success = await saveUserChat(username, chatData);

    if (success) {
      return NextResponse.json({ success: true });
    } else {
      return NextResponse.json(
        { error: true, message: "Failed to save chat data" },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error("Error saving user chat data:", error);
    return NextResponse.json(
      { error: true, message: "Internal server error" },
      { status: 500 }
    );
  }
}
