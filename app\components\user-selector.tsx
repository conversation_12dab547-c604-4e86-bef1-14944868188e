import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useUserStore } from "../store/user";
import { useAccessStore } from "../store";
import { useAppConfig } from "../store/config";
import { useChatStore } from "../store/chat";
import { Path } from "../constant";
import styles from "./user-selector.module.scss";
import { IconButton } from "./button";
// eslint-disable-next-line unused-imports/no-unused-imports
import { Input, SingleLineInput, List, ListItem, Modal, showToast, showConfirm } from "./ui-lib";
import AddIcon from "../icons/add.svg";
import BotIcon from "../icons/bot.svg";
import EditIcon from "../icons/edit.svg";
import { PasswordInput, InputRow } from "./password-input";
import { ServiceProvider } from "@/app/constant";
// 移除 fs 和 path 模块，因为它们是 Node.js 模块，不能在浏览器端使用

export function UserSelector() {
  const navigate = useNavigate();
  const userStore = useUserStore();
  const accessStore = useAccessStore();
  const chatStore = useChatStore();
  const [users, setUsers] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showNewUserModal, setShowNewUserModal] = useState(false);
  const [showChangePasswordModal, setShowChangePasswordModal] = useState(false);
  const [newUsername, setNewUsername] = useState("");
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [currentPassword, setCurrentPassword] = useState("");
  const [passwordError, setPasswordError] = useState("");

  // 加载用户列表
  useEffect(() => {
    const loadUsers = async () => {
      try {
        setIsLoading(true);
        // 从服务器获取用户列表
        const response = await fetch("/api/user-config/list", {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
          },
        });

        if (response.ok) {
          const data = await response.json();
          // 确保默认用户不在普通用户列表中
          const userList = data.users || [];
          setUsers(userList);
        } else {
          console.error("Failed to load users:", response.statusText);
          // 如果API调用失败，至少显示已知的用户
          setUsers(userStore.users.map(u => u.username));
        }
      } catch (error) {
        console.error("Error loading users:", error);
        // 如果出错，至少显示已知的用户
        setUsers(userStore.users.map(u => u.username));
      } finally {
        setIsLoading(false);
      }
    };

    loadUsers();
  }, [userStore.users]);

  // 处理用户选择
  const handleUserSelect = async (username: string) => {
    try {
      // 如果当前有登录用户，先保存当前用户的对话数据
      if (userStore.isLoggedIn() && userStore.currentUser) {
        await chatStore.saveUserChatData(userStore.currentUser.username);
      }

      // 如果是默认用户，直接登录
      if (username === "default") {
        await userStore.login(username);
      } else {
        // 非默认用户需要密码验证，跳转到登录页面
        navigate(`${Path.Auth}?userLogin=true`, { state: { username } });
        return; // 中断后续流程
      }

      // 尝试加载用户配置
      const userConfig = await userStore.getUserConfig(username);
      if (userConfig) {
        console.log(`[UserSelector] Loaded config for user ${username}`);

        // 应用用户配置到 accessStore
        const userConfigAny = userConfig as any;

        accessStore.update((access) => {
          // 恢复自定义配置状态
          if (userConfigAny.useCustomConfig !== undefined) {
            access.useCustomConfig = userConfigAny.useCustomConfig;
          }

          // 恢复所有 API 配置
          if (userConfigAny.openaiApiKey !== undefined) {
            access.openaiApiKey = userConfigAny.openaiApiKey;
          }
          if (userConfigAny.openaiUrl !== undefined) {
            access.openaiUrl = userConfigAny.openaiUrl;
          }

          if (userConfigAny.googleApiKey !== undefined) {
            access.googleApiKey = userConfigAny.googleApiKey;
          }
          if (userConfigAny.googleUrl !== undefined) {
            access.googleUrl = userConfigAny.googleUrl;
          }

          if (userConfigAny.anthropicApiKey !== undefined) {
            access.anthropicApiKey = userConfigAny.anthropicApiKey;
          }
          if (userConfigAny.anthropicUrl !== undefined) {
            access.anthropicUrl = userConfigAny.anthropicUrl;
          }

          if (userConfigAny.deepseekApiKey !== undefined) {
            access.deepseekApiKey = userConfigAny.deepseekApiKey;
          }
          if (userConfigAny.deepseekUrl !== undefined) {
            access.deepseekUrl = userConfigAny.deepseekUrl;
          }

          if (userConfigAny.siliconflowApiKey !== undefined) {
            access.siliconflowApiKey = userConfigAny.siliconflowApiKey;
          }
          if (userConfigAny.siliconflowUrl !== undefined) {
            access.siliconflowUrl = userConfigAny.siliconflowUrl;
          }

          if (userConfigAny.azureApiKey !== undefined) {
            access.azureApiKey = userConfigAny.azureApiKey;
          }
          if (userConfigAny.azureUrl !== undefined) {
            access.azureUrl = userConfigAny.azureUrl;
          }
          if (userConfigAny.azureApiVersion !== undefined) {
            access.azureApiVersion = userConfigAny.azureApiVersion;
          }

          if (userConfigAny.baiduApiKey !== undefined) {
            access.baiduApiKey = userConfigAny.baiduApiKey;
          }
          if (userConfigAny.baiduUrl !== undefined) {
            access.baiduUrl = userConfigAny.baiduUrl;
          }
          if (userConfigAny.baiduSecretKey !== undefined) {
            access.baiduSecretKey = userConfigAny.baiduSecretKey;
          }

          if (userConfigAny.bytedanceApiKey !== undefined) {
            access.bytedanceApiKey = userConfigAny.bytedanceApiKey;
          }
          if (userConfigAny.bytedanceUrl !== undefined) {
            access.bytedanceUrl = userConfigAny.bytedanceUrl;
          }

          if (userConfigAny.alibabaApiKey !== undefined) {
            access.alibabaApiKey = userConfigAny.alibabaApiKey;
          }
          if (userConfigAny.alibabaUrl !== undefined) {
            access.alibabaUrl = userConfigAny.alibabaUrl;
          }

          if (userConfigAny.moonshotApiKey !== undefined) {
            access.moonshotApiKey = userConfigAny.moonshotApiKey;
          }
          if (userConfigAny.moonshotUrl !== undefined) {
            access.moonshotUrl = userConfigAny.moonshotUrl;
          }

          if (userConfigAny.stabilityApiKey !== undefined) {
            access.stabilityApiKey = userConfigAny.stabilityApiKey;
          }
          if (userConfigAny.stabilityUrl !== undefined) {
            access.stabilityUrl = userConfigAny.stabilityUrl;
          }

          if (userConfigAny.tencentSecretKey !== undefined) {
            access.tencentSecretKey = userConfigAny.tencentSecretKey;
          }
          if (userConfigAny.tencentSecretId !== undefined) {
            access.tencentSecretId = userConfigAny.tencentSecretId;
          }
          if (userConfigAny.tencentUrl !== undefined) {
            access.tencentUrl = userConfigAny.tencentUrl;
          }

          if (userConfigAny.iflytekApiKey !== undefined) {
            access.iflytekApiKey = userConfigAny.iflytekApiKey;
          }
          if (userConfigAny.iflytekApiSecret !== undefined) {
            access.iflytekApiSecret = userConfigAny.iflytekApiSecret;
          }
          if (userConfigAny.iflytekUrl !== undefined) {
            access.iflytekUrl = userConfigAny.iflytekUrl;
          }

          if (userConfigAny.xaiApiKey !== undefined) {
            access.xaiApiKey = userConfigAny.xaiApiKey;
          }
          if (userConfigAny.xaiUrl !== undefined) {
            access.xaiUrl = userConfigAny.xaiUrl;
          }

          if (userConfigAny.chatglmApiKey !== undefined) {
            access.chatglmApiKey = userConfigAny.chatglmApiKey;
          }
          if (userConfigAny.chatglmUrl !== undefined) {
            access.chatglmUrl = userConfigAny.chatglmUrl;
          }

          if (userConfigAny.accessCode !== undefined) {
            access.accessCode = userConfigAny.accessCode;
          }
        });

        // 应用模型配置
        if (userConfigAny.modelConfig) {
          accessStore.update((access) => {
            if (userConfigAny.modelConfig.providerName) {
              access.provider = userConfigAny.modelConfig.providerName;
            }
          });
        }

        // 应用完整的应用配置
        const appConfig = useAppConfig.getState();
        useAppConfig.setState({
          ...appConfig,
          // 应用用户配置中的所有设置
          submitKey: userConfig.submitKey || appConfig.submitKey,
          avatar: userConfig.avatar || appConfig.avatar,
          fontSize: userConfig.fontSize || appConfig.fontSize,
          fontFamily: userConfig.fontFamily || appConfig.fontFamily,
          theme: userConfig.theme || appConfig.theme,
          tightBorder: userConfig.tightBorder !== undefined ? userConfig.tightBorder : appConfig.tightBorder,
          sendPreviewBubble: userConfig.sendPreviewBubble !== undefined ? userConfig.sendPreviewBubble : appConfig.sendPreviewBubble,
          enableAutoGenerateTitle: userConfig.enableAutoGenerateTitle !== undefined ? userConfig.enableAutoGenerateTitle : appConfig.enableAutoGenerateTitle,
          sidebarWidth: userConfig.sidebarWidth || appConfig.sidebarWidth,
          enableArtifacts: userConfig.enableArtifacts !== undefined ? userConfig.enableArtifacts : appConfig.enableArtifacts,
          enableCodeFold: userConfig.enableCodeFold !== undefined ? userConfig.enableCodeFold : appConfig.enableCodeFold,
          disablePromptHint: userConfig.disablePromptHint !== undefined ? userConfig.disablePromptHint : appConfig.disablePromptHint,
          dontShowMaskSplashScreen: userConfig.dontShowMaskSplashScreen !== undefined ? userConfig.dontShowMaskSplashScreen : appConfig.dontShowMaskSplashScreen,
          hideBuiltinMasks: userConfig.hideBuiltinMasks !== undefined ? userConfig.hideBuiltinMasks : appConfig.hideBuiltinMasks,
          customModels: userConfig.customModels || appConfig.customModels,
          modelConfig: userConfig.modelConfig ? {
            ...appConfig.modelConfig,
            ...userConfig.modelConfig,
          } : appConfig.modelConfig,
          ttsConfig: userConfig.ttsConfig ? {
            ...appConfig.ttsConfig,
            ...userConfig.ttsConfig,
          } : appConfig.ttsConfig,
        });

        console.log(`[UserSelector] Applied full config for user ${username}, theme: ${userConfig.theme}`);
      } else {
        console.log(`[UserSelector] No config found for user ${username}, using default settings`);

        // 应用默认的SiliconFlow API密钥（如果用户没有配置的话）
        accessStore.update((access) => {
          // 只有在用户没有配置API密钥时才设置默认值
          if (!access.siliconflowApiKey) {
            // 注意：这个默认密钥可能需要更新，建议用户配置自己的API密钥
            access.siliconflowApiKey = "sk-gvumbcqueizqwylhmzxcrblrioeieiogphqreeheteuzwgym";
            console.log("[UserSelector] Applied default SiliconFlow API key - please configure your own key in settings");
          }
          access.provider = ServiceProvider.SiliconFlow;
        });
      }

      // 确保访问码字段有值，即使是空字符串
      accessStore.update((access) => {
        if (access.accessCode === undefined || access.accessCode === null) {
          access.accessCode = "";
        }
      });

      // 加载用户的对话数据
      await chatStore.loadUserChatData(username);
      console.log(`[UserSelector] Loaded chat data for user ${username}`);

      // 登录成功后跳转到聊天页面
      navigate(Path.Chat);
    } catch (error) {
      console.error("Login failed:", error);
      showToast(`登录失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  };

  // 处理创建新用户
  const handleCreateUser = async () => {
    if (!newUsername.trim()) {
      showToast("请输入用户名");
      return;
    }

    // 验证密码
    if (!newPassword) {
      setPasswordError("请输入密码");
      return;
    }

    if (newPassword.length < 6) {
      setPasswordError("密码长度至少为6位");
      return;
    }

    if (newPassword !== confirmPassword) {
      setPasswordError("两次输入的密码不一致");
      return;
    }

    try {
      // 如果当前有登录用户，先保存当前用户的对话数据
      if (userStore.isLoggedIn() && userStore.currentUser) {
        await chatStore.saveUserChatData(userStore.currentUser.username);
      }

      // 创建新用户
      await userStore.login(newUsername);

      // 应用默认的SiliconFlow API密钥
      accessStore.update((access) => {
        access.siliconflowApiKey = "sk-gvumbcqueizqwylhmzxcrblrioeieiogphqreeheteuzwgym";
        access.provider = ServiceProvider.SiliconFlow;
        access.accessCode = "";
      });

      // 保存默认配置
      const defaultConfig = {
        lastUpdate: Date.now(),
        submitKey: "Enter" as any, // 使用类型断言解决类型问题
        avatar: "1f603",
        fontSize: 14,
        fontFamily: "",
        theme: "auto" as any, // 使用类型断言解决类型问题
        tightBorder: false,
        sendPreviewBubble: true,
        enableAutoGenerateTitle: true,
        sidebarWidth: 300,
        enableArtifacts: true,
        enableCodeFold: true,
        disablePromptHint: false,
        dontShowMaskSplashScreen: false,
        hideBuiltinMasks: false,
        customModels: "",
        modelConfig: {
          model: "deepseek-chat",
          providerName: "SiliconFlow" as any, // 使用类型断言解决类型问题
          temperature: 0.5,
          top_p: 1,
          max_tokens: 4000,
          presence_penalty: 0,
          frequency_penalty: 0,
          sendMemory: true,
          historyMessageCount: 4,
          compressMessageLengthThreshold: 1000,
          compressModel: "",
          compressProviderName: "",
          enableInjectSystemPrompts: true,
          template: "{{input}}",
          size: "1024x1024" as any, // 使用类型断言解决类型问题
          quality: "standard" as any, // 使用类型断言解决类型问题
          style: "vivid" as any // 使用类型断言解决类型问题
        },
        ttsConfig: {
          enable: false,
          autoplay: false,
          engine: "OpenAI-TTS" as any, // 使用类型断言解决类型问题
          model: "tts-1",
          voice: "alloy" as any, // 使用类型断言解决类型问题
          speed: 1
        },
        openaiApiKey: "",
        googleApiKey: "",
        anthropicApiKey: "",
        siliconflowApiKey: "sk-gvumbcqueizqwylhmzxcrblrioeieiogphqreeheteuzwgym" // 修正属性名
      };

      // 应用完整的应用配置
      useAppConfig.setState({
        ...defaultConfig
      });

      console.log(`[UserSelector] Applied default config for new user ${newUsername}, theme: ${defaultConfig.theme}`);

      // 添加密码到配置中
      const configWithPassword = {
        ...defaultConfig,
        password: newPassword // 保存密码
      };

      // 使用类型断言解决类型问题
      await userStore.saveUserConfig(newUsername, configWithPassword as any);

      // 创建新用户的空对话数据
      chatStore.clearSessions();
      await chatStore.saveUserChatData(newUsername);
      console.log(`[UserSelector] Created empty chat data for new user ${newUsername}`);

      // 关闭模态框
      setShowNewUserModal(false);
      setNewUsername("");
      setNewPassword("");
      setConfirmPassword("");
      setPasswordError("");

      // 刷新用户列表
      setUsers([...users, newUsername]);

      // 登录成功后跳转到聊天页面
      navigate(Path.Chat);
    } catch (error) {
      console.error("Create user failed:", error);
      showToast(`创建用户失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  };

  // 处理密码修改
  const handleChangePassword = async () => {
    if (!userStore.currentUser || userStore.currentUser.username === "default") {
      showToast("默认用户不需要密码");
      return;
    }

    // 验证当前密码
    const userConfig = await userStore.getUserConfig(userStore.currentUser.username);
    if (!userConfig) {
      showToast("获取用户配置失败");
      return;
    }

    if (userConfig.password !== currentPassword) {
      setPasswordError("当前密码错误");
      return;
    }

    // 验证新密码
    if (!newPassword) {
      setPasswordError("请输入新密码");
      return;
    }

    if (newPassword.length < 6) {
      setPasswordError("密码长度至少为6位");
      return;
    }

    if (newPassword !== confirmPassword) {
      setPasswordError("两次输入的密码不一致");
      return;
    }

    try {
      // 更新密码
      const updatedConfig = {
        ...userConfig,
        password: newPassword
      };

      await userStore.saveUserConfig(userStore.currentUser.username, updatedConfig);
      showToast("密码修改成功");

      // 关闭模态框
      setShowChangePasswordModal(false);
      setCurrentPassword("");
      setNewPassword("");
      setConfirmPassword("");
      setPasswordError("");
    } catch (error) {
      console.error("[UserSelector] Failed to change password:", error);
      showToast(`密码修改失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  };

  // 处理用户删除
  const handleDeleteUser = async (username: string) => {
    if (username === "default") {
      showToast("不能删除默认用户");
      return;
    }

    const confirmed = await showConfirm(`确认删除用户 "${username}"，此操作不可撤销。`);
    if (!confirmed) return;

    try {
      // 删除用户配置文件
      // 这里需要实现服务器端的删除用户API
      showToast("用户删除功能尚未实现");
    } catch (error) {
      console.error("[UserSelector] Failed to delete user:", error);
      showToast(`删除用户失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  };

  // 获取上次登录的用户名
  const getLastLoginUser = () => {
    // 如果有用户列表，找出最后登录的用户
    if (userStore.users && userStore.users.length > 0) {
      // 按照最后登录时间排序，获取最近登录的非默认用户
      const sortedUsers = [...userStore.users]
        .filter(user => user.username !== "default")
        .sort((a, b) => b.lastLoginTime - a.lastLoginTime);

      if (sortedUsers.length > 0) {
        return sortedUsers[0].username;
      }
    }
    return ""; // 如果没有找到，返回空字符串
  };

  // 处理已注册用户登录
  const handleRegisteredUserLogin = () => {
    const lastUsername = getLastLoginUser();
    navigate(`${Path.Auth}?userLogin=true`, { state: { username: lastUsername } });
  };

  return (
    <div className={styles["user-selector"]}>
      <div className={styles["user-selector-header"]}>
        <h2>请选择用户</h2>
        <div className={styles["header-buttons"]}>
          {userStore.isLoggedIn() && userStore.currentUser && userStore.currentUser.username !== "default" && (
            <IconButton
              icon={<EditIcon />}
              text="修改密码"
              onClick={() => setShowChangePasswordModal(true)}
            />
          )}
          <IconButton
            icon={<AddIcon />}
            text="用户注册"
            onClick={() => setShowNewUserModal(true)}
          />
        </div>
      </div>

      {isLoading ? (
        <div className={styles["loading"]}>加载中...</div>
      ) : (
        <div className={styles["user-list"]}>
          {/* 默认用户选项 */}
          <div
            key="default"
            className={`${styles["user-item"]} ${styles["default-user"]}`}
            onClick={() => handleUserSelect("default")}
          >
            <BotIcon />
            <span>默认用户</span>
          </div>

          {/* 已注册用户登录按钮 */}
          <div
            key="registered-user"
            className={styles["user-item"]}
            style={{ backgroundColor: "var(--second)", border: "2px solid var(--primary)" }}
            onClick={handleRegisteredUserLogin}
          >
            <BotIcon />
            <span>已注册用户</span>
          </div>
        </div>
      )}

      {showNewUserModal && (
        <Modal
          title="新用户注册"
          onClose={() => setShowNewUserModal(false)}
          actions={[
            <IconButton
              key="cancel"
              text="取消"
              onClick={() => setShowNewUserModal(false)}
            />,
            <IconButton
              key="create"
              type="primary"
              text="创建"
              onClick={handleCreateUser}
              disabled={!newUsername.trim()}
            />
          ]}
        >
          <InputRow label="用户名：">
            <SingleLineInput
              value={newUsername}
              placeholder="请输入新用户名"
              onChange={(e) => setNewUsername(e.currentTarget.value)}
              maxLength={20}
              style={{ maxWidth: "300px" }} /* 增加宽度 */
            />
          </InputRow>

          <InputRow label="密码：">
            <PasswordInput
              value={newPassword}
              placeholder="请输入密码（至少6位）"
              onChange={(e) => {
                setNewPassword(e.currentTarget.value);
                setPasswordError("");
              }}
              style={{ maxWidth: "300px" }} /* 增加宽度 */
            />
          </InputRow>

          <InputRow label="确认密码：">
            <PasswordInput
              value={confirmPassword}
              placeholder="请再次输入密码"
              onChange={(e) => {
                setConfirmPassword(e.currentTarget.value);
                setPasswordError("");
              }}
              style={{ maxWidth: "300px" }} /* 增加宽度 */
            />
          </InputRow>

          {passwordError && (
            <div style={{ color: "red", marginBottom: "15px" }}>{passwordError}</div>
          )}
        </Modal>
      )}

      {showChangePasswordModal && (
        <Modal
          title="修改密码"
          onClose={() => {
            setShowChangePasswordModal(false);
            setCurrentPassword("");
            setNewPassword("");
            setConfirmPassword("");
            setPasswordError("");
          }}
          actions={[
            <IconButton
              key="cancel"
              text="取消"
              onClick={() => {
                setShowChangePasswordModal(false);
                setCurrentPassword("");
                setNewPassword("");
                setConfirmPassword("");
                setPasswordError("");
              }}
            />,
            <IconButton
              key="change"
              type="primary"
              text="修改"
              onClick={handleChangePassword}
              disabled={!currentPassword || !newPassword || !confirmPassword}
            />
          ]}
        >
          <InputRow label="当前密码：">
            <PasswordInput
              value={currentPassword}
              placeholder="请输入当前密码"
              onChange={(e) => {
                setCurrentPassword(e.currentTarget.value);
                setPasswordError("");
              }}
              style={{ maxWidth: "300px" }} /* 增加宽度 */
            />
          </InputRow>

          <InputRow label="新密码：">
            <PasswordInput
              value={newPassword}
              placeholder="请输入新密码（至少6位）"
              onChange={(e) => {
                setNewPassword(e.currentTarget.value);
                setPasswordError("");
              }}
              style={{ maxWidth: "300px" }} /* 增加宽度 */
            />
          </InputRow>

          <InputRow label="确认密码：">
            <PasswordInput
              value={confirmPassword}
              placeholder="请再次输入新密码"
              onChange={(e) => {
                setConfirmPassword(e.currentTarget.value);
                setPasswordError("");
              }}
              style={{ maxWidth: "300px" }} /* 增加宽度 */
            />
          </InputRow>

          {passwordError && (
            <div style={{ color: "red", marginBottom: "15px" }}>{passwordError}</div>
          )}
        </Modal>
      )}
    </div>
  );
}













