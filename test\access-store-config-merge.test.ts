/**
 * 测试 access store 中配置合并的逻辑
 * 验证当用户设置了自定义接口配置时，服务器配置不会覆盖用户的自定义设置
 */

// 简化的测试，不依赖复杂的 mock

describe("Access Store Config Merge Logic", () => {
  // 测试配置合并的核心逻辑
  function mergeConfigLogic(currentState: any, serverConfig: any) {
    // 模拟 DEFAULT_ACCESS_STATE
    const DEFAULT_ACCESS_STATE = {
      accessCode: "",
      useCustomConfig: false,
      customModels: "",
      defaultModel: "",
      visionModels: "",
      openaiUrl: "",
      openaiApiKey: "",
      // 其他默认值...
    };

    // 定义需要保留的用户自定义配置字段
    const userCustomFields = [
      'useCustomConfig',
      'openaiUrl', 'openaiApiKey',
      'azureUrl', 'azureApiKey', 'azureApiVersion',
      'googleUrl', 'googleApiKey', 'googleApiVersion',
      'anthropicUrl', 'anthropicApiKey', 'anthropicApiVersion',
      'baiduUrl', 'baiduApiKey', 'baiduSecretKey',
      'bytedanceUrl', 'bytedanceApiKey',
      'alibabaUrl', 'alibabaApiKey',
      'moonshotUrl', 'moonshotApiKey',
      'stabilityUrl', 'stabilityApiKey',
      'tencentUrl', 'tencentSecretKey', 'tencentSecretId',
      'iflytekUrl', 'iflytekApiKey', 'iflytekApiSecret',
      'deepseekUrl', 'deepseekApiKey',
      'xaiUrl', 'xaiApiKey',
      'chatglmUrl', 'chatglmApiKey',
      'siliconflowUrl', 'siliconflowApiKey',
      'accessCode'
    ];

    // 需要特别处理的字段（这些字段需要同时更新到 config store）
    const specialFields = ['customModels', 'defaultModel', 'visionModels'];

    // 创建合并后的配置对象
    const mergedConfig = { ...serverConfig };

    // 检测用户是否有自定义配置（通过检查是否有自定义URL或API Key）
    const hasCustomConfig = currentState.useCustomConfig ||
      userCustomFields.some(field => {
        if (field === 'useCustomConfig' || field === 'accessCode') return false;
        return currentState[field] !== undefined &&
               currentState[field] !== '' &&
               currentState[field] !== DEFAULT_ACCESS_STATE[field];
      });

    // 检查用户是否有自定义的特殊字段（如 customModels）
    const hasCustomSpecialFields = specialFields.some(field => {
      return currentState[field] !== undefined &&
             currentState[field] !== '' &&
             currentState[field] !== DEFAULT_ACCESS_STATE[field];
    });

    // 如果用户有自定义配置，保留用户的自定义设置
    if (hasCustomConfig) {
      userCustomFields.forEach(field => {
        if (currentState[field] !== undefined && currentState[field] !== '') {
          mergedConfig[field] = currentState[field];
        }
      });
      // 确保 useCustomConfig 被设置为 true
      mergedConfig.useCustomConfig = true;
    } else {
      // 如果用户没有自定义配置，仍然保留 accessCode
      if (currentState.accessCode !== undefined && currentState.accessCode !== '') {
        mergedConfig.accessCode = currentState.accessCode;
      }
      // 确保 useCustomConfig 保持正确的状态
      mergedConfig.useCustomConfig = currentState.useCustomConfig || false;
    }

    // 处理特殊字段：如果用户有自定义的特殊字段，保留它们
    if (hasCustomSpecialFields) {
      specialFields.forEach(field => {
        if (currentState[field] !== undefined &&
            currentState[field] !== '' &&
            currentState[field] !== DEFAULT_ACCESS_STATE[field]) {
          mergedConfig[field] = currentState[field];
        }
      });
    }

    return mergedConfig;
  }

  test("should preserve user custom config when server config is fetched", () => {
    // 模拟用户已设置的自定义配置
    const mockCurrentState = {
      useCustomConfig: true,
      openaiUrl: "https://custom-api.example.com",
      openaiApiKey: "user-custom-key",
      accessCode: "user-access-code",
      provider: "OpenAI",
      // 其他默认配置
      needCode: true,
      hideUserApiKey: false,
      disableGPT4: false,
    };

    // 模拟服务器返回的配置
    const mockServerConfig = {
      needCode: false,
      hideUserApiKey: true,
      disableGPT4: true,
      hideBalanceQuery: false,
      disableFastLink: false,
      customModels: "",
      defaultModel: "gpt-4",
      visionModels: "",
    };

    // 执行合并逻辑
    const result = mergeConfigLogic(mockCurrentState, mockServerConfig);

    // 验证结果
    expect(result).toEqual({
      // 服务器配置应该被应用
      needCode: false,
      hideUserApiKey: true,
      disableGPT4: true,
      hideBalanceQuery: false,
      disableFastLink: false,
      customModels: "",
      defaultModel: "gpt-4",
      visionModels: "",
      // 用户的自定义配置应该被保留
      useCustomConfig: true,
      openaiUrl: "https://custom-api.example.com",
      openaiApiKey: "user-custom-key",
      accessCode: "user-access-code",
    });
  });

  test("should preserve accessCode even when useCustomConfig is false", () => {
    // 模拟用户没有启用自定义配置但设置了 accessCode
    const mockCurrentState = {
      useCustomConfig: false,
      openaiUrl: "",
      openaiApiKey: "",
      accessCode: "user-access-code",
      provider: "OpenAI",
      needCode: true,
    };

    // 模拟服务器返回的配置
    const mockServerConfig = {
      needCode: false,
      hideUserApiKey: true,
      accessCode: "", // 服务器不应该覆盖用户的 accessCode
    };

    const result = mergeConfigLogic(mockCurrentState, mockServerConfig);

    // 验证 accessCode 被保留
    expect(result.accessCode).toBe("user-access-code");
    expect(result.needCode).toBe(false); // 服务器配置应该被应用
  });

  test("should not preserve empty custom config values", () => {
    // 模拟用户启用了自定义配置但某些字段为空
    const mockCurrentState = {
      useCustomConfig: true,
      openaiUrl: "", // 空值不应该被保留
      openaiApiKey: "user-key", // 非空值应该被保留
      accessCode: "user-access-code",
    };

    const mockServerConfig = {
      openaiUrl: "https://default-api.openai.com",
      openaiApiKey: "",
      needCode: false,
    };

    const result = mergeConfigLogic(mockCurrentState, mockServerConfig);

    // 空的 openaiUrl 不应该覆盖服务器的默认值
    expect(result.openaiUrl).toBe("https://default-api.openai.com");
    // 非空的 openaiApiKey 应该被保留
    expect(result.openaiApiKey).toBe("user-key");
    // accessCode 应该被保留
    expect(result.accessCode).toBe("user-access-code");
  });

  test("should preserve custom models when user has set them", () => {
    // 模拟用户设置了自定义模型
    const mockCurrentState = {
      useCustomConfig: false, // 即使没有启用自定义配置
      customModels: "custom-model-1,custom-model-2", // 用户设置的自定义模型
      defaultModel: "",
      visionModels: "",
      accessCode: "",
    };

    const mockServerConfig = {
      customModels: "server-model-1,server-model-2", // 服务器的默认模型
      defaultModel: "gpt-4",
      visionModels: "gpt-4-vision",
      needCode: false,
    };

    const result = mergeConfigLogic(mockCurrentState, mockServerConfig);

    // 用户的自定义模型应该被保留
    expect(result.customModels).toBe("custom-model-1,custom-model-2");
    // 服务器的其他配置应该被应用
    expect(result.needCode).toBe(false);
    expect(result.defaultModel).toBe("gpt-4");
    expect(result.visionModels).toBe("gpt-4-vision");
  });

  test("should use server models when user has no custom models", () => {
    // 模拟用户没有设置自定义模型
    const mockCurrentState = {
      useCustomConfig: false,
      customModels: "", // 用户没有设置自定义模型
      defaultModel: "",
      visionModels: "",
      accessCode: "",
    };

    const mockServerConfig = {
      customModels: "server-model-1,server-model-2", // 服务器的默认模型
      defaultModel: "gpt-4",
      visionModels: "gpt-4-vision",
      needCode: false,
    };

    const result = mergeConfigLogic(mockCurrentState, mockServerConfig);

    // 应该使用服务器的模型配置
    expect(result.customModels).toBe("server-model-1,server-model-2");
    expect(result.defaultModel).toBe("gpt-4");
    expect(result.visionModels).toBe("gpt-4-vision");
    expect(result.needCode).toBe(false);
  });
});
