import { NextRequest, NextResponse } from "next/server";
import fs from "fs/promises";
import path from "path";
import { auth } from "../../api/auth";
import { ModelProvider } from "../../constant";

const USER_CONFIG_DIR = path.join(process.cwd(), "app/users");

// 确保用户配置目录存在
async function ensureUserConfigDir() {
  try {
    await fs.mkdir(USER_CONFIG_DIR, { recursive: true });
    return true;
  } catch (error) {
    console.error("Failed to create user config directory:", error);
    return false;
  }
}

// 获取用户配置文件路径
function getUserConfigPath(username: string): string {
  return path.join(USER_CONFIG_DIR, `${username}.json`);
}

// 获取用户配置
async function getUserConfig(username: string) {
  try {
    const configPath = getUserConfigPath(username);
    const configData = await fs.readFile(configPath, 'utf-8');
    return JSON.parse(configData);
  } catch (error) {
    console.log(`No config found for user ${username}, trying to load default config`);

    // 尝试加载默认配置
    try {
      const defaultConfigPath = path.join(USER_CONFIG_DIR, 'default.json');
      const defaultConfigData = await fs.readFile(defaultConfigPath, 'utf-8');
      return JSON.parse(defaultConfigData);
    } catch (defaultError) {
      console.log('No default config found either, returning null');
      return null;
    }
  }
}

// 保存用户配置
async function saveUserConfig(username: string, config: any) {
  try {
    await ensureUserConfigDir();
    const configPath = getUserConfigPath(username);
    await fs.writeFile(configPath, JSON.stringify(config, null, 2), 'utf-8');
    return true;
  } catch (error) {
    console.error(`Failed to save config for user ${username}:`, error);
    return false;
  }
}

// 处理请求
export async function GET(req: NextRequest) {
  // 验证用户身份
  const authResult = auth(req, ModelProvider.GPT);
  if (authResult.error) {
    return NextResponse.json(authResult, {
      status: 401,
    });
  }

  // 从URL参数中获取用户名
  const url = new URL(req.url);
  const username = url.searchParams.get("username");

  if (!username) {
    return NextResponse.json(
      { error: true, message: "Username is required" },
      { status: 400 }
    );
  }

  // 获取用户配置
  const config = await getUserConfig(username);

  if (config) {
    return NextResponse.json(config);
  } else {
    return NextResponse.json(
      { error: true, message: "User config not found" },
      { status: 404 }
    );
  }
}

export async function POST(req: NextRequest) {
  // 验证用户身份
  const authResult = auth(req, ModelProvider.GPT);
  if (authResult.error) {
    return NextResponse.json(authResult, {
      status: 401,
    });
  }

  try {
    // 解析请求体
    const body = await req.json();
    const { username, config } = body;

    if (!username || !config) {
      return NextResponse.json(
        { error: true, message: "Username and config are required" },
        { status: 400 }
      );
    }

    // 保存用户配置
    const success = await saveUserConfig(username, config);

    if (success) {
      return NextResponse.json({ success: true });
    } else {
      return NextResponse.json(
        { error: true, message: "Failed to save user config" },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error("Error saving user config:", error);
    return NextResponse.json(
      { error: true, message: "Internal server error" },
      { status: 500 }
    );
  }
}
