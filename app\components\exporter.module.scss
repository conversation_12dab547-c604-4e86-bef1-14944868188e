.message-exporter {
  &-body {
    margin-top: 20px;
  }
}

.export-content {
  white-space: break-spaces;
  padding: 10px !important;
}

.steps {
  background-color: var(--gray);
  border-radius: 10px;
  overflow: hidden;
  padding: 5px;
  position: relative;
  box-shadow: var(--card-shadow) inset;

  .steps-progress {
    $padding: 5px;
    height: calc(100% - 2 * $padding);
    width: calc(100% - 2 * $padding);
    position: absolute;
    top: $padding;
    left: $padding;

    &-inner {
      box-sizing: border-box;
      box-shadow: var(--card-shadow);
      border: var(--border-in-light);
      content: "";
      display: inline-block;
      width: 0%;
      height: 100%;
      background-color: var(--white);
      transition: all ease 0.3s;
      border-radius: 8px;
    }
  }

  .steps-inner {
    display: flex;
    transform: scale(1);

    .step {
      flex-grow: 1;
      padding: 5px 10px;
      font-size: 14px;
      color: var(--black);
      opacity: 0.5;
      transition: all ease 0.3s;

      display: flex;
      align-items: center;
      justify-content: center;

      $radius: 8px;

      &-finished {
        opacity: 0.9;
      }

      &:hover {
        opacity: 0.8;
      }

      &-current {
        color: var(--primary);
      }

      .step-index {
        background-color: var(--gray);
        border: var(--border-in-light);
        border-radius: 6px;
        display: inline-block;
        padding: 0px 5px;
        font-size: 12px;
        margin-right: 8px;
        opacity: 0.8;
      }

      .step-name {
        font-size: 12px;
      }
    }
  }
}

.preview-actions {
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;

  button {
    flex-grow: 1;

    &:not(:last-child) {
      margin-right: 10px;
    }
  }
}

.image-previewer {
  .preview-body {
    border-radius: 10px;
    padding: 20px;
    box-shadow: var(--card-shadow) inset;
    background-color: var(--gray);

    .chat-info {
      background-color: var(--second);
      padding: 20px;
      border-radius: 10px;
      margin-bottom: 20px;
      display: flex;
      justify-content: space-between;
      align-items: flex-end;
      position: relative;
      overflow: hidden;

      @media screen and (max-width: 600px) {
        flex-direction: column;
        align-items: flex-start;

        .icons {
          margin-bottom: 20px;
        }
      }

      .logo {
        position: absolute;
        top: 0px;
        left: 0px;
        height: 50%;
        transform: scale(1.5);
      }

      .main-title {
        font-size: 20px;
        font-weight: bolder;
      }

      .sub-title {
        font-size: 12px;
      }

      .icons {
        margin-top: 10px;
        display: flex;
        align-items: center;

        .icon-space {
          font-size: 12px;
          margin: 0 10px;
          font-weight: bolder;
          color: var(--primary);
        }
      }

      .chat-info-item {
        font-size: 12px;
        color: var(--primary);
        padding: 2px 15px;
        border-radius: 10px;
        background-color: var(--white);
        box-shadow: var(--card-shadow);

        &:not(:last-child) {
          margin-bottom: 5px;
        }
      }
    }

    .message {
      margin-bottom: 20px;
      display: flex;

      .avatar {
        margin-right: 10px;
      }

      .body {
        border-radius: 10px;
        padding: 8px 10px;
        max-width: calc(100% - 104px);
        box-shadow: var(--card-shadow);
        border: var(--border-in-light);

        code,
        pre {
          overflow: hidden;
        }

        .message-image {
          width: 100%;
          margin-top: 10px;
        }

        .message-images {
          display: grid;
          justify-content: left;
          grid-gap: 10px;
          grid-template-columns: repeat(var(--image-count), auto);
          margin-top: 10px;
        }

        @media screen and (max-width: 600px) {
          $image-width: calc(calc(100vw/2)/var(--image-count));

          .message-image-multi {
            width: $image-width;
            height: $image-width;
          }

          .message-image {
            max-width: calc(100vw/3*2);
          }
        }

        @media screen and (min-width: 600px) {
          $max-image-width: calc(900px/3*2/var(--image-count));
          $image-width: calc(80vw/3*2/var(--image-count));

          .message-image-multi {
            width: $image-width;
            height: $image-width;
            max-width: $max-image-width;
            max-height: $max-image-width;
          }

          .message-image {
            max-width: calc(100vw/3*2);
          }
        }

        .message-image-multi {
          object-fit: cover;
        }

        .message-image,
        .message-image-multi {
          box-sizing: border-box;
          border-radius: 10px;
          border: rgba($color: #888, $alpha: 0.2) 1px solid;
        }
      }

      &-assistant {
        .body {
          background-color: var(--white);
        }
      }

      &-user {
        flex-direction: row-reverse;

        .avatar {
          margin-right: 0;
        }

        .body {
          background-color: var(--second);
          margin-right: 10px;
        }
      }
    }
  }

  .default-theme {}
}