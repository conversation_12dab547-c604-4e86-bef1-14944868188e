import { useState } from "react";
import styles from "./password-input.module.scss";
import EyeIcon from "../icons/eye.svg";
import EyeOffIcon from "../icons/eye-off.svg";

interface PasswordInputProps {
  value: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  placeholder?: string;
  onKeyDown?: (e: React.KeyboardEvent<HTMLInputElement>) => void;
  className?: string;
  style?: React.CSSProperties;
}

export function PasswordInput(props: PasswordInputProps) {
  const [visible, setVisible] = useState(false);

  const toggleVisibility = () => {
    setVisible(!visible);
  };

  return (
    <div className={styles["password-input-container"]} style={props.style}>
      <input
        type={visible ? "text" : "password"}
        value={props.value}
        onChange={props.onChange}
        placeholder={props.placeholder}
        onKeyDown={props.onKeyDown}
        className={`${styles["password-input"]} ${props.className || ""}`}
      />
      <div 
        className={styles["password-eye"]} 
        onClick={toggleVisibility}
        title={visible ? "隐藏密码" : "显示密码"}
      >
        {visible ? <EyeIcon /> : <EyeOffIcon />}
      </div>
    </div>
  );
}

interface InputRowProps {
  label: string;
  children: React.ReactNode;
}

export function InputRow(props: InputRowProps) {
  return (
    <div className={styles["input-row"]}>
      <div className={styles["input-label"]}>{props.label}</div>
      <div className={styles["input-field"]}>{props.children}</div>
    </div>
  );
}
