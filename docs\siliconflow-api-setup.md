# SiliconFlow API 配置指南

## 问题描述

如果您遇到以下错误：
```
POST http://127.0.0.1:3030/api/siliconflow/v1/chat/completions 401 (Unauthorized)
生成摘要出错,无法生成正确的摘要信息
```

这表明SiliconFlow API密钥认证失败。

## 解决方案

### 方案1：配置服务器端API密钥（推荐）

1. 获取SiliconFlow API密钥：
   - 访问 [SiliconFlow官网](https://cloud.siliconflow.cn/)
   - 注册账号并获取API密钥

2. 配置环境变量：
   - 编辑项目根目录的 `.env` 文件
   - 找到 `SILICONFLOW_API_KEY=` 这一行
   - 将您的API密钥填入：
     ```
     SILICONFLOW_API_KEY=sk-your-actual-api-key-here
     ```

3. 重启应用：
   ```bash
   npm run dev
   ```

### 方案2：配置用户端API密钥

1. 在应用中打开设置页面（点击右上角设置图标）

2. 找到"模型服务商"部分，选择"SiliconFlow"

3. 在"SiliconFlow API Key"字段中输入您的API密钥

4. 点击"验证"按钮确认密钥有效

5. 保存设置

## 验证配置

配置完成后，您可以：

1. 尝试生成新的对话标题
2. 使用摘要功能
3. 检查控制台是否还有401错误

## 常见问题

### Q: 我已经配置了API密钥，但仍然出现401错误
A: 请检查：
- API密钥是否正确复制（没有多余的空格）
- API密钥是否已过期
- 账户是否有足够的余额
- 是否选择了正确的模型

### Q: 如何获取SiliconFlow API密钥？
A: 
1. 访问 https://cloud.siliconflow.cn/
2. 注册并登录账号
3. 在控制台中创建API密钥
4. 复制密钥并配置到应用中

### Q: 服务器端配置和用户端配置有什么区别？
A: 
- **服务器端配置**：所有用户共享同一个API密钥，由管理员配置
- **用户端配置**：每个用户可以使用自己的API密钥

## 技术细节

当发生401错误时，系统会：

1. 记录详细的错误信息到控制台
2. 显示用户友好的错误提示
3. 尝试使用备用模型（如果配置了的话）
4. 提供配置建议

错误处理已经优化，会提供更清晰的错误信息和解决建议。
