import { useState, useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { Path } from "../constant";
import { IconButton } from "./button";
import { ErrorBoundary } from "./error";
import { useUserStore } from "../store/user";
import { useChatStore } from "../store";
import { useAppConfig } from "../store/config";
import { showToast, SingleLineInput } from "./ui-lib";
import styles from "./user-selector.module.scss";
import CloseIcon from "../icons/close.svg";
import { PasswordInput, InputRow } from "./password-input";

export function UserLogin() {
  const navigate = useNavigate();
  const location = useLocation();
  const userStore = useUserStore();
  const chatStore = useChatStore();

  const [username, setUsername] = useState("");
  const [password, setPassword] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const [isNewUser, setIsNewUser] = useState(false);
  const [confirmPassword, setConfirmPassword] = useState("");

  useEffect(() => {
    // 从路由状态中获取用户名
    if (location.state && location.state.username) {
      setUsername(location.state.username);
    }
  }, [location.state]);

  // 处理用户登录
  const handleLogin = async () => {
    if (!username) {
      setError("请输入用户名");
      return;
    }

    if (!password) {
      setError("请输入密码");
      return;
    }

    if (isNewUser) {
      if (password !== confirmPassword) {
        setError("两次输入的密码不一致");
        return;
      }

      // 检查用户是否已存在
      try {
        const existingConfig = await userStore.getUserConfig(username);
        if (existingConfig) {
          setError("用户名已存在，请使用其他用户名");
          return;
        }
      } catch (error) {
        // 用户不存在，可以继续注册
      }
    } else {
      // 非注册模式下，验证用户是否存在
      try {
        const userConfig = await userStore.getUserConfig(username);
        if (!userConfig) {
          setError("用户不存在，请检查用户名或注册新用户");
          return;
        }
      } catch (error) {
        setError("验证用户时出错，请稍后再试");
        console.error("[UserLogin] Error checking user:", error);
        return;
      }
    }

    setIsLoading(true);
    setError("");

    try {
      if (isNewUser) {
        // 创建新用户
        // 获取当前应用配置作为基础
        const appConfig = useAppConfig?.getState() || {};

        // 创建用户配置，包含密码
        const configWithPassword = {
          ...appConfig,
          password: password,
        };

        // 保存用户配置
        await userStore.saveUserConfig(username, configWithPassword);

        // 登录新用户
        await userStore.login(username);

        // 创建新用户的空对话数据
        chatStore.clearSessions();
        await chatStore.saveUserChatData(username);

        showToast("用户创建成功");
      } else {
        // 登录现有用户
        // 获取用户配置
        const userConfig = await userStore.getUserConfig(username);

        if (!userConfig) {
          setError("用户不存在");
          setIsLoading(false);
          return;
        }

        // 验证密码
        if (userConfig.password !== password) {
          setError("密码错误");
          setIsLoading(false);
          return;
        }

        // 如果当前有登录用户，先保存当前用户的对话数据
        if (userStore.isLoggedIn() && userStore.currentUser) {
          await chatStore.saveUserChatData(userStore.currentUser.username);
        }

        // 登录用户
        await userStore.login(username);

        // 应用用户配置
        if (userConfig) {
          console.log(`[UserLogin] Applying user config for ${username}`);

          // 应用主题
          document.body.classList.remove("light", "dark");
          document.body.classList.add(userConfig.theme);
        }

        // 加载用户的对话数据
        await chatStore.loadUserChatData(username);
        console.log(`[UserLogin] Loaded chat data for user ${username}`);
      }

      // 登录成功，跳转到主页
      navigate(Path.Home);
    } catch (error) {
      console.error("[UserLogin] Login failed:", error);
      setError(`${isNewUser ? '注册' : '登录'}失败: ${error instanceof Error ? error.message : '未知错误'}`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <ErrorBoundary>
      <div className={styles["user-selector"]}>
        <div className={styles["user-selector-header"]}>
          <h2>{isNewUser ? "用户注册" : "用户登录"}</h2>
          <div className={styles["header-buttons"]}>
            <IconButton
              icon={<CloseIcon />}
              onClick={() => navigate(Path.Home)}
              bordered
            />
          </div>
        </div>

        <div style={{ maxWidth: "1000px", margin: "0 auto", width: "100%" }}>
          <InputRow label="用户名：">
            <SingleLineInput
              value={username}
              placeholder="用户名"
              onChange={(e) => {
                setUsername(e.currentTarget.value);
                setError("");
              }}
              maxLength={20}
              style={{ maxWidth: "300px" }}
            />
          </InputRow>

          <InputRow label="密码：">
            <PasswordInput
              value={password}
              placeholder="密码"
              onChange={(e) => {
                setPassword(e.currentTarget.value);
                setError("");
              }}
              onKeyDown={(e) => {
                if (e.key === "Enter" && !isNewUser) {
                  handleLogin();
                }
              }}
              style={{ maxWidth: "300px" }}
            />
          </InputRow>

          {isNewUser && (
            <InputRow label="确认密码：">
              <PasswordInput
                value={confirmPassword}
                placeholder="再次输入密码"
                onChange={(e) => {
                  setConfirmPassword(e.currentTarget.value);
                  setError("");
                }}
                onKeyDown={(e) => {
                  if (e.key === "Enter") {
                    handleLogin();
                  }
                }}
                style={{ maxWidth: "300px" }}
              />
            </InputRow>
          )}

          {error && (
            <div style={{
              color: "red",
              marginBottom: "15px",
              textAlign: "center",
              padding: "10px",
              backgroundColor: "rgba(255, 0, 0, 0.05)",
              borderRadius: "5px",
              border: "1px solid rgba(255, 0, 0, 0.2)"
            }}>
              {error}
            </div>
          )}

          <div style={{ display: "flex", justifyContent: "center", gap: "10px", marginTop: "20px" }}>
            <IconButton
              text="返回"
              onClick={() => navigate(Path.Home)}
              bordered
            />
            <IconButton
              text={isLoading ? (isNewUser ? "注册中..." : "登录中...") : (isNewUser ? "注册" : "登录")}
              type="primary"
              onClick={handleLogin}
              disabled={isLoading || !username || !password || (isNewUser && !confirmPassword)}
            />
            <IconButton
              text={isNewUser ? "已有账号？去登录" : "没有账号？去注册"}
              onClick={() => {
                setIsNewUser(!isNewUser);
                setError("");
              }}
              bordered
            />
          </div>
        </div>
      </div>
    </ErrorBoundary>
  );
}

