import { NextRequest, NextResponse } from "next/server";
import fs from "fs/promises";
import path from "path";

const USER_CONFIG_DIR = path.join(process.cwd(), "app/users");

// 获取用户列表
async function getUserList() {
  try {
    // 确保目录存在
    try {
      await fs.mkdir(USER_CONFIG_DIR, { recursive: true });
    } catch (error) {
      console.error("Failed to create user config directory:", error);
    }

    // 读取目录内容
    const files = await fs.readdir(USER_CONFIG_DIR);

    // 过滤出JSON文件并去掉扩展名，排除default.json
    const users = files
      .filter(file => file.endsWith('.json') && file !== 'default.json')
      .map(file => file.replace('.json', ''));

    return users;
  } catch (error) {
    console.error("Failed to get user list:", error);
    return [];
  }
}

// 处理请求
export async function GET(req: NextRequest) {
  try {
    // 获取用户列表
    const users = await getUserList();

    return NextResponse.json({ users });
  } catch (error) {
    console.error("Error getting user list:", error);
    return NextResponse.json(
      { error: true, message: "Internal server error" },
      { status: 500 }
    );
  }
}
