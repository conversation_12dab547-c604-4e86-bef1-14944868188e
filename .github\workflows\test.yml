name: Run Tests

on:
  push:
    branches:
      - main
    tags:
      - "!*"
  pull_request:
    types:
      - review_requested

jobs:
  test:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: 18
          cache: "yarn"

      - name: Cache node_modules
        uses: actions/cache@v4
        with:
          path: node_modules
          key: ${{ runner.os }}-node_modules-${{ hashFiles('**/yarn.lock') }}
          restore-keys: |
            ${{ runner.os }}-node_modules-

      - name: Install dependencies
        run: yarn install

      - name: Run Jest tests
        run: yarn test:ci
