import { useEffect } from "react";
import { useAccessStore } from "../store";
import { useAppConfig } from "../store/config";
import { useUserStore } from "../store/user";
import { UserConfig } from "../store/user";
import { DEFAULT_CONFIG } from "../store/config";
import { DEFAULT_ACCESS_STATE } from "../store/access";

// 深度比较两个对象，返回只包含不同值的对象
function getDifferentValues(current: any, defaults: any): any {
  if (current === null || current === undefined) return undefined;
  if (defaults === null || defaults === undefined) return current;

  // 如果是基本类型，直接比较
  if (typeof current !== 'object' || typeof defaults !== 'object') {
    return current === defaults ? undefined : current;
  }

  // 如果是数组，直接比较
  if (Array.isArray(current) || Array.isArray(defaults)) {
    return JSON.stringify(current) === JSON.stringify(defaults) ? undefined : current;
  }

  const result: any = {};
  let hasChanges = false;

  // 遍历当前对象的所有属性
  for (const key in current) {
    if (current.hasOwnProperty(key)) {
      const currentValue = current[key];
      const defaultValue = defaults[key];

      if (typeof currentValue === 'object' && currentValue !== null &&
          typeof defaultValue === 'object' && defaultValue !== null &&
          !Array.isArray(currentValue) && !Array.isArray(defaultValue)) {
        // 递归处理嵌套对象
        const nestedDiff = getDifferentValues(currentValue, defaultValue);
        if (nestedDiff !== undefined && Object.keys(nestedDiff).length > 0) {
          result[key] = nestedDiff;
          hasChanges = true;
        }
      } else {
        // 比较基本类型和数组
        if (currentValue !== defaultValue) {
          // 特殊处理：空字符串和undefined视为相同（对于API key等字段）
          if (!(currentValue === "" && (defaultValue === "" || defaultValue === undefined)) &&
              !(currentValue === undefined && defaultValue === "")) {
            result[key] = currentValue;
            hasChanges = true;
          }
        }
      }
    }
  }

  return hasChanges ? result : undefined;
}

// 过滤用户配置，只保存与默认值不同的部分
function filterUserConfig(userConfig: any): any {
  const filtered: any = {};

  // 总是保留密码字段（如果存在）
  if (userConfig.password !== undefined) {
    filtered.password = userConfig.password;
  }

  // 比较并保存与DEFAULT_CONFIG不同的配置
  const configDiff = getDifferentValues(
    {
      submitKey: userConfig.submitKey,
      avatar: userConfig.avatar,
      fontSize: userConfig.fontSize,
      fontFamily: userConfig.fontFamily,
      theme: userConfig.theme,
      tightBorder: userConfig.tightBorder,
      sendPreviewBubble: userConfig.sendPreviewBubble,
      enableAutoGenerateTitle: userConfig.enableAutoGenerateTitle,
      sidebarWidth: userConfig.sidebarWidth,
      enableArtifacts: userConfig.enableArtifacts,
      enableCodeFold: userConfig.enableCodeFold,
      disablePromptHint: userConfig.disablePromptHint,
      dontShowMaskSplashScreen: userConfig.dontShowMaskSplashScreen,
      hideBuiltinMasks: userConfig.hideBuiltinMasks,
      customModels: userConfig.customModels,
      modelConfig: userConfig.modelConfig,
      ttsConfig: userConfig.ttsConfig,
      realtimeConfig: userConfig.realtimeConfig,
    },
    {
      submitKey: DEFAULT_CONFIG.submitKey,
      avatar: DEFAULT_CONFIG.avatar,
      fontSize: DEFAULT_CONFIG.fontSize,
      fontFamily: DEFAULT_CONFIG.fontFamily,
      theme: DEFAULT_CONFIG.theme,
      tightBorder: DEFAULT_CONFIG.tightBorder,
      sendPreviewBubble: DEFAULT_CONFIG.sendPreviewBubble,
      enableAutoGenerateTitle: DEFAULT_CONFIG.enableAutoGenerateTitle,
      sidebarWidth: DEFAULT_CONFIG.sidebarWidth,
      enableArtifacts: DEFAULT_CONFIG.enableArtifacts,
      enableCodeFold: DEFAULT_CONFIG.enableCodeFold,
      disablePromptHint: DEFAULT_CONFIG.disablePromptHint,
      dontShowMaskSplashScreen: DEFAULT_CONFIG.dontShowMaskSplashScreen,
      hideBuiltinMasks: DEFAULT_CONFIG.hideBuiltinMasks,
      customModels: DEFAULT_CONFIG.customModels,
      modelConfig: DEFAULT_CONFIG.modelConfig,
      ttsConfig: DEFAULT_CONFIG.ttsConfig,
      realtimeConfig: DEFAULT_CONFIG.realtimeConfig,
    }
  );

  if (configDiff) {
    Object.assign(filtered, configDiff);
  }

  // 比较并保存与DEFAULT_ACCESS_STATE不同的访问配置
  const accessDiff = getDifferentValues(
    {
      accessCode: userConfig.accessCode,
      useCustomConfig: userConfig.useCustomConfig,
      provider: userConfig.provider,
      openaiUrl: userConfig.openaiUrl,
      openaiApiKey: userConfig.openaiApiKey,
      azureUrl: userConfig.azureUrl,
      azureApiKey: userConfig.azureApiKey,
      azureApiVersion: userConfig.azureApiVersion,
      googleUrl: userConfig.googleUrl,
      googleApiKey: userConfig.googleApiKey,
      googleApiVersion: userConfig.googleApiVersion,
      googleSafetySettings: userConfig.googleSafetySettings,
      anthropicUrl: userConfig.anthropicUrl,
      anthropicApiKey: userConfig.anthropicApiKey,
      anthropicApiVersion: userConfig.anthropicApiVersion,
      baiduUrl: userConfig.baiduUrl,
      baiduApiKey: userConfig.baiduApiKey,
      baiduSecretKey: userConfig.baiduSecretKey,
      bytedanceUrl: userConfig.bytedanceUrl,
      bytedanceApiKey: userConfig.bytedanceApiKey,
      alibabaUrl: userConfig.alibabaUrl,
      alibabaApiKey: userConfig.alibabaApiKey,
      moonshotUrl: userConfig.moonshotUrl,
      moonshotApiKey: userConfig.moonshotApiKey,
      stabilityUrl: userConfig.stabilityUrl,
      stabilityApiKey: userConfig.stabilityApiKey,
      tencentUrl: userConfig.tencentUrl,
      tencentSecretKey: userConfig.tencentSecretKey,
      tencentSecretId: userConfig.tencentSecretId,
      iflytekUrl: userConfig.iflytekUrl,
      iflytekApiKey: userConfig.iflytekApiKey,
      iflytekApiSecret: userConfig.iflytekApiSecret,
      deepseekUrl: userConfig.deepseekUrl,
      deepseekApiKey: userConfig.deepseekApiKey,
      xaiUrl: userConfig.xaiUrl,
      xaiApiKey: userConfig.xaiApiKey,
      chatglmUrl: userConfig.chatglmUrl,
      chatglmApiKey: userConfig.chatglmApiKey,
      siliconflowUrl: userConfig.siliconflowUrl,
      siliconflowApiKey: userConfig.siliconflowApiKey,
      edgeTTSVoiceName: userConfig.edgeTTSVoiceName,
    },
    {
      accessCode: DEFAULT_ACCESS_STATE.accessCode,
      useCustomConfig: DEFAULT_ACCESS_STATE.useCustomConfig,
      provider: DEFAULT_ACCESS_STATE.provider,
      openaiUrl: DEFAULT_ACCESS_STATE.openaiUrl,
      openaiApiKey: DEFAULT_ACCESS_STATE.openaiApiKey,
      azureUrl: DEFAULT_ACCESS_STATE.azureUrl,
      azureApiKey: DEFAULT_ACCESS_STATE.azureApiKey,
      azureApiVersion: DEFAULT_ACCESS_STATE.azureApiVersion,
      googleUrl: DEFAULT_ACCESS_STATE.googleUrl,
      googleApiKey: DEFAULT_ACCESS_STATE.googleApiKey,
      googleApiVersion: DEFAULT_ACCESS_STATE.googleApiVersion,
      googleSafetySettings: DEFAULT_ACCESS_STATE.googleSafetySettings,
      anthropicUrl: DEFAULT_ACCESS_STATE.anthropicUrl,
      anthropicApiKey: DEFAULT_ACCESS_STATE.anthropicApiKey,
      anthropicApiVersion: DEFAULT_ACCESS_STATE.anthropicApiVersion,
      baiduUrl: DEFAULT_ACCESS_STATE.baiduUrl,
      baiduApiKey: DEFAULT_ACCESS_STATE.baiduApiKey,
      baiduSecretKey: DEFAULT_ACCESS_STATE.baiduSecretKey,
      bytedanceUrl: DEFAULT_ACCESS_STATE.bytedanceUrl,
      bytedanceApiKey: DEFAULT_ACCESS_STATE.bytedanceApiKey,
      alibabaUrl: DEFAULT_ACCESS_STATE.alibabaUrl,
      alibabaApiKey: DEFAULT_ACCESS_STATE.alibabaApiKey,
      moonshotUrl: DEFAULT_ACCESS_STATE.moonshotUrl,
      moonshotApiKey: DEFAULT_ACCESS_STATE.moonshotApiKey,
      stabilityUrl: DEFAULT_ACCESS_STATE.stabilityUrl,
      stabilityApiKey: DEFAULT_ACCESS_STATE.stabilityApiKey,
      tencentUrl: DEFAULT_ACCESS_STATE.tencentUrl,
      tencentSecretKey: DEFAULT_ACCESS_STATE.tencentSecretKey,
      tencentSecretId: DEFAULT_ACCESS_STATE.tencentSecretId,
      iflytekUrl: DEFAULT_ACCESS_STATE.iflytekUrl,
      iflytekApiKey: DEFAULT_ACCESS_STATE.iflytekApiKey,
      iflytekApiSecret: DEFAULT_ACCESS_STATE.iflytekApiSecret,
      deepseekUrl: DEFAULT_ACCESS_STATE.deepseekUrl,
      deepseekApiKey: DEFAULT_ACCESS_STATE.deepseekApiKey,
      xaiUrl: DEFAULT_ACCESS_STATE.xaiUrl,
      xaiApiKey: DEFAULT_ACCESS_STATE.xaiApiKey,
      chatglmUrl: DEFAULT_ACCESS_STATE.chatglmUrl,
      chatglmApiKey: DEFAULT_ACCESS_STATE.chatglmApiKey,
      siliconflowUrl: DEFAULT_ACCESS_STATE.siliconflowUrl,
      siliconflowApiKey: DEFAULT_ACCESS_STATE.siliconflowApiKey,
      edgeTTSVoiceName: DEFAULT_ACCESS_STATE.edgeTTSVoiceName,
    }
  );

  if (accessDiff) {
    Object.assign(filtered, accessDiff);
  }

  return Object.keys(filtered).length > 0 ? filtered : {};
}

// 这个组件负责监听配置变化并保存到用户配置文件
export function UserConfigSaver() {
  const userStore = useUserStore();
  const accessStore = useAccessStore();
  const appConfig = useAppConfig();

  // 监听配置变化并保存
  useEffect(() => {
    // 检查是否在浏览器环境中
    if (typeof window === 'undefined') {
      return;
    }

    // 如果用户未登录，不执行任何操作
    if (!userStore || !userStore.isLoggedIn || !userStore.isLoggedIn() || !userStore.currentUser) {
      console.log("[UserConfigSaver] User not logged in, skipping config save setup");
      return;
    }

    try {
      // 获取当前用户名，在 effect 内部获取以避免依赖问题
      const username = userStore.currentUser.username;
      if (!username) {
        console.error("[UserConfigSaver] Username is empty");
        return;
      }

      console.log(`[UserConfigSaver] Setting up config save for user: ${username}`);

      // 创建一个防抖函数，避免频繁保存
      let saveTimeout: NodeJS.Timeout | null = null;

      const saveConfig = () => {
        if (saveTimeout) {
          clearTimeout(saveTimeout);
        }

        saveTimeout = setTimeout(async () => {
          try {
            // 检查用户是否仍然登录
            if (!userStore.isLoggedIn() || !userStore.currentUser) {
              console.log("[UserConfigSaver] User no longer logged in, skipping save");
              return;
            }

            // 先获取现有的用户配置，确保保留密码
            const existingConfig = await userStore.getUserConfig(username);

            // 构建完整的用户配置
            const fullUserConfig = {
              ...appConfig,
              // 添加 accessStore 中的所有重要配置
              useCustomConfig: accessStore.useCustomConfig,
              provider: accessStore.provider,
              openaiApiKey: accessStore.openaiApiKey || "",
              openaiUrl: accessStore.openaiUrl || "",
              googleApiKey: accessStore.googleApiKey || "",
              googleUrl: accessStore.googleUrl || "",
              googleApiVersion: accessStore.googleApiVersion || "",
              googleSafetySettings: accessStore.googleSafetySettings,
              anthropicApiKey: accessStore.anthropicApiKey || "",
              anthropicUrl: accessStore.anthropicUrl || "",
              anthropicApiVersion: accessStore.anthropicApiVersion || "",
              deepseekApiKey: accessStore.deepseekApiKey || "",
              deepseekUrl: accessStore.deepseekUrl || "",
              siliconflowApiKey: accessStore.siliconflowApiKey || "",
              siliconflowUrl: accessStore.siliconflowUrl || "",
              azureApiKey: accessStore.azureApiKey || "",
              azureUrl: accessStore.azureUrl || "",
              azureApiVersion: accessStore.azureApiVersion || "",
              baiduApiKey: accessStore.baiduApiKey || "",
              baiduUrl: accessStore.baiduUrl || "",
              baiduSecretKey: accessStore.baiduSecretKey || "",
              bytedanceApiKey: accessStore.bytedanceApiKey || "",
              bytedanceUrl: accessStore.bytedanceUrl || "",
              alibabaApiKey: accessStore.alibabaApiKey || "",
              alibabaUrl: accessStore.alibabaUrl || "",
              moonshotApiKey: accessStore.moonshotApiKey || "",
              moonshotUrl: accessStore.moonshotUrl || "",
              stabilityApiKey: accessStore.stabilityApiKey || "",
              stabilityUrl: accessStore.stabilityUrl || "",
              tencentSecretKey: accessStore.tencentSecretKey || "",
              tencentSecretId: accessStore.tencentSecretId || "",
              tencentUrl: accessStore.tencentUrl || "",
              iflytekApiKey: accessStore.iflytekApiKey || "",
              iflytekApiSecret: accessStore.iflytekApiSecret || "",
              iflytekUrl: accessStore.iflytekUrl || "",
              xaiApiKey: accessStore.xaiApiKey || "",
              xaiUrl: accessStore.xaiUrl || "",
              chatglmApiKey: accessStore.chatglmApiKey || "",
              chatglmUrl: accessStore.chatglmUrl || "",
              accessCode: accessStore.accessCode || "",
              edgeTTSVoiceName: accessStore.edgeTTSVoiceName || "",
              // 保留原有密码
              password: existingConfig?.password || "",
            };

            // 过滤配置，只保存与默认值不同的部分
            const userConfig = filterUserConfig(fullUserConfig) as UserConfig;

            // 保存用户配置
            await userStore.saveUserConfig(username, userConfig);
            console.log(`[UserConfigSaver] Auto-saved filtered config for user ${username}, size: ${JSON.stringify(userConfig).length} bytes`);
          } catch (error) {
            console.error("[UserConfig] Failed to save user config:", error);
          }
        }, 2000); // 2秒防抖
      };

      // 使用 useEffect 监听 accessStore 和 appConfig 的变化
      // 这些值会在组件内部被跟踪，当它们变化时会触发 saveConfig

      // 当 accessStore 或 appConfig 变化时，触发保存
      const accessStoreLastUpdateTime = accessStore.lastUpdateTime;
      const appConfigLastUpdate = appConfig.lastUpdate;

      // eslint-disable-next-line
      useEffect(() => {
        saveConfig();
        // eslint-disable-next-line react-hooks/exhaustive-deps
      }, [accessStoreLastUpdateTime, appConfigLastUpdate]);

      // 组件卸载时清理
      return () => {
        if (saveTimeout) {
          clearTimeout(saveTimeout);
        }
      };
    } catch (error) {
      console.error("[UserConfigSaver] Error setting up config save:", error);
      return () => {}; // 返回空清理函数
    }
  }, [userStore, accessStore, appConfig]);

  // 这是一个纯功能组件，不渲染任何内容
  return null;
}


