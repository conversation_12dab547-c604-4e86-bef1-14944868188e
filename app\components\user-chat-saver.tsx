import { useEffect, useState } from "react";
import { useChatStore } from "../store/chat";
import { useUserStore } from "../store/user";

// 这个组件负责监听对话变化并保存到用户对话文件
export function UserChatSaver() {
  const userStore = useUserStore();
  const chatStore = useChatStore();
  const [lastSaveTime, setLastSaveTime] = useState(Date.now());

  // 获取当前会话和会话索引，用于监听变化
  const currentSession = chatStore.currentSession();
  const currentSessionIndex = chatStore.currentSessionIndex;
  const sessions = chatStore.sessions;

  // 监听对话变化并保存
  useEffect(() => {
    // 检查是否在浏览器环境中
    if (typeof window === 'undefined') {
      return;
    }

    // 如果用户未登录，不执行任何操作
    if (!userStore || !userStore.isLoggedIn || !userStore.isLoggedIn() || !userStore.currentUser) {
      console.log("[UserChatSaver] User not logged in, skipping chat save setup");
      return;
    }

    const username = userStore.currentUser.username;

    // 防抖保存，避免频繁保存
    const now = Date.now();
    if (now - lastSaveTime < 2000) { // 2秒内不重复保存
      return;
    }

    // 设置定时器延迟保存
    const saveTimeout = setTimeout(async () => {
      try {
        // 检查用户是否仍然登录
        if (!userStore.isLoggedIn() || !userStore.currentUser) {
          console.log("[UserChatSaver] User no longer logged in, skipping save");
          return;
        }

        // 保存用户对话数据
        await chatStore.saveUserChatData(username);
        console.log(`[UserChatSaver] Saved chat data for user ${username}`);
        setLastSaveTime(Date.now());
      } catch (error) {
        console.error("[UserChatSaver] Failed to save user chat data:", error);
      }
    }, 2000); // 2秒防抖

    // 在页面卸载前保存
    const handleBeforeUnload = async () => {
      if (userStore.isLoggedIn() && userStore.currentUser) {
        try {
          await chatStore.saveUserChatData(userStore.currentUser.username);
          console.log(`[UserChatSaver] Saved chat data for user ${userStore.currentUser.username} before unload`);
        } catch (error) {
          console.error("[UserChatSaver] Failed to save user chat data before unload:", error);
        }
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);

    // 清理函数
    return () => {
      clearTimeout(saveTimeout);
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [userStore, chatStore, currentSession, currentSessionIndex, sessions, lastSaveTime]);

  // 这个组件不渲染任何内容
  return null;
}
