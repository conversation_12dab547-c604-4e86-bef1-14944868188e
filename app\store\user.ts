// eslint-disable-next-line unused-imports/no-unused-imports
import { create } from "zustand";
// eslint-disable-next-line unused-imports/no-unused-imports
import { persist } from "zustand/middleware";
import { StoreKey } from "../constant";
import { createPersistStore } from "../utils/store";
import { ChatConfig } from "./config";
import { getHeaders } from "../client/api";

export interface UserConfig extends ChatConfig {
  // 用户特定的配置项可以在这里添加
  password?: string; // 用户密码，仅对非默认用户有效
}

export interface UserInfo {
  username: string;
  lastLoginTime: number;
}

export interface UserState {
  currentUser: UserInfo | null;
  users: UserInfo[];

  login: (username: string) => Promise<void>;
  logout: () => void;
  getUserConfig: (username: string) => Promise<UserConfig | null>;
  saveUserConfig: (username: string, config: UserConfig) => Promise<void>;
  isLoggedIn: () => boolean;
}

export const useUserStore = createPersistStore(
  {
    currentUser: null as UserInfo | null,
    users: [] as UserInfo[],
  },
  (set, get) => ({
    login: async (username: string) => {
      const now = Date.now();
      const userInfo: UserInfo = {
        username,
        lastLoginTime: now,
      };

      // 更新用户列表
      const users = get().users;
      const existingUserIndex = users.findIndex(u => u.username === username);

      if (existingUserIndex >= 0) {
        // 更新现有用户
        users[existingUserIndex] = userInfo;
      } else {
        // 添加新用户
        users.push(userInfo);
      }

      set({
        currentUser: userInfo,
        users,
      });
    },

    logout: () => {
      set({ currentUser: null });
    },

    getUserConfig: async (username: string): Promise<UserConfig | null> => {
      // 检查是否在浏览器环境中
      if (typeof window === 'undefined') {
        console.log('getUserConfig called on server side, returning null');
        return null;
      }

      try {
        // 从服务器获取用户配置
        const response = await fetch(`/api/user-config?username=${encodeURIComponent(username)}`, {
          method: "GET",
          headers: {
            ...getHeaders(),
          },
        });

        if (!response.ok) {
          if (response.status === 404) {
            console.log(`No config found for user ${username}, will use default config`);
            return null;
          }
          throw new Error(`Failed to get user config: ${response.statusText}`);
        }

        const config = await response.json();
        return config as UserConfig;
      } catch (error) {
        console.error(`Error getting config for user ${username}:`, error);
        return null;
      }
    },

    saveUserConfig: async (username: string, config: UserConfig): Promise<void> => {
      // 检查是否在浏览器环境中
      if (typeof window === 'undefined') {
        console.log('saveUserConfig called on server side, skipping');
        return;
      }

      try {
        // 将用户配置保存到服务器
        const response = await fetch("/api/user-config", {
          method: "POST",
          headers: {
            ...getHeaders(),
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ username, config }),
        });

        if (!response.ok) {
          throw new Error(`Failed to save user config: ${response.statusText}`);
        }
      } catch (error) {
        console.error(`Failed to save config for user ${username}:`, error);
        throw error;
      }
    },

    isLoggedIn: () => {
      return get().currentUser !== null;
    },
  }),
  {
    name: StoreKey.User,
    version: 1.0,
  },
);
