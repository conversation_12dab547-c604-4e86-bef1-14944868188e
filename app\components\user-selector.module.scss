@import "../styles/animation.scss";

.user-selector {
  display: flex;
  flex-direction: column;
  width: 100%;
  max-width: 600px;
  margin: 0 auto;
  padding: 20px;
  border-radius: 8px;
  background-color: var(--white);
  box-shadow: var(--card-shadow);

  .container {
    max-width: 500px;
    margin: 0 auto;
    width: 100%;
  }

  .user-selector-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    h2 {
      margin: 0;
      font-size: 1.5rem;
      color: var(--black);
    }

    .header-buttons {
      display: flex;
      gap: 10px;
    }
  }

  .loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100px;
    font-size: 1rem;
    color: var(--black);
  }

  .user-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 15px;

    .user-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 15px;
      border-radius: 8px;
      background-color: var(--second);
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-5px);
        box-shadow: var(--card-shadow);
      }

      svg {
        width: 40px;
        height: 40px;
        margin-bottom: 10px;
        color: var(--primary);
      }

      span {
        font-size: 1rem;
        font-weight: 500;
        color: var(--black);
        text-align: center;
        word-break: break-word;
      }

      &.default-user {
        background-color: var(--primary);
        border: 2px solid var(--primary);

        svg {
          color: var(--white);
        }

        span {
          color: var(--white);
          font-weight: 600;
        }
      }
    }
  }

  .no-users {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100px;
    font-size: 1rem;
    color: var(--black);
    text-align: center;
  }
}

:global(html.dark) {
  .user-selector {
    background-color: var(--dark-50);

    .user-selector-header {
      h2 {
        color: var(--white);
      }
    }

    .loading {
      color: var(--white);
    }

    .user-list {
      .user-item {
        background-color: var(--dark-30);

        span {
          color: var(--white);
        }

        &.default-user {
          background-color: var(--primary);
          border: 2px solid var(--primary);

          svg {
            color: var(--white);
          }

          span {
            color: var(--white);
            font-weight: 600;
          }
        }
      }
    }

    .no-users {
      color: var(--white);
    }
  }
}
